import { useState, useEffect, useCallback } from "react";
import {
  SignedIn,
  SignedOut,
  useClerk,
  useUser,
  useSSO,
} from "@clerk/clerk-expo";
import { router } from "expo-router";
import { createHandleErrorDialog } from "@/lib/errors";
import { z } from "zod";
import { API_URLS, fetchApi } from "@/config/api";
import { TFunction } from "i18next";
import { useTranslation } from "react-i18next";
import * as WebBrowser from "expo-web-browser";
import * as AuthSession from "expo-auth-session";
import * as Linking from "expo-linking";

export const myUserProfileSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    id: z.string({
      invalid_type_error: t("profile.validation.invalid_type", {
        field: t("profile.fields.id"),
      }),
    }),
    name: z.string({
      invalid_type_error: t("profile.validation.invalid_type", {
        field: t("profile.fields.first_name"),
      }),
    }),
    firstName: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.first_name"),
        }),
      })
      .min(
        2,
        t("profile.validation.min", {
          field: t("profile.fields.first_name"),
          min: 2,
        })
      )
      .max(
        50,
        t("profile.validation.max", {
          field: t("profile.fields.first_name"),
          max: 50,
        })
      ),
    lastName: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.last_name"),
        }),
      })
      .min(
        2,
        t("profile.validation.min", {
          field: t("profile.fields.last_name"),
          min: 2,
        })
      )
      .max(
        50,
        t("profile.validation.max", {
          field: t("profile.fields.last_name"),
          max: 50,
        })
      ),
    email: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.email"),
        }),
      })
      .email(
        t("profile.validation.invalid_type", {
          field: t("profile.fields.email"),
        })
      ),
    emailVerified: z.boolean(),
    phoneNumber: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.phone_number"),
        }),
        required_error: t("profile.validation.required", {
          field: t("profile.fields.phone_number"),
        }),
      })
      .transform((value) => {
        const cleaned = value.replace(/[^\d+]/g, "");

        if (!cleaned.startsWith("+")) {
          return `+244${cleaned}`;
        }

        return cleaned;
      })
      .refine(
        (value) => {
          const phoneRegex = /^\+[1-9][0-9]{0,2}[0-9]{9,11}$/;
          return phoneRegex.test(value);
        },
        {
          message: t("profile.validation.invalid_format", {
            field: t("profile.fields.phone_number"),
            format: "+244912345678, +49151234567, +351912345678",
          }),
        }
      ),
    website: z
      .string({
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.website"),
        }),
      })
      .url(
        t("profile.validation.invalid_type", {
          field: t("profile.fields.website"),
        })
      )
      .optional(),
    gender: z
      .enum(["male", "female", "prefer_not_say"], {
        invalid_type_error: t("profile.validation.invalid_type", {
          field: t("profile.fields.gender"),
        }),
        required_error: t("profile.validation.required", {
          field: t("profile.fields.gender"),
        }),
      })
      .default("prefer_not_say"),
    birthday: z.coerce.date({
      errorMap: (issue, ctx) => {
        if (issue.code === "invalid_type") {
          return {
            message: t("profile.validation.invalid_type", {
              field: t("profile.fields.birth_date"),
            }),
          };
        }
        if (issue.code === "invalid_date") {
          return {
            message: t("profile.validation.invalid_type", {
              field: t("profile.fields.birth_date"),
            }),
          };
        }
        return { message: ctx.defaultError };
      },
    }),
  });

export type MyUserProfileType = z.infer<ReturnType<typeof myUserProfileSchema>>;

const toMyUserProfile = (user: any): MyUserProfileType | null => {
  if (!user) return null;
  return {
    id: user.id,
    email: user.emailAddresses[0].emailAddress,
    emailVerified: user.emailAddresses[0].verification.status === "verified",
    name: user.fullName,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.unsafeMetadata.phoneNumber,
    website: user.unsafeMetadata.website,
    gender: user.unsafeMetadata.gender,
    birthday: new Date(user.unsafeMetadata.birthday),
  };
};

export const useWarmUpBrowser = () => {
  useEffect(() => {
    // Warm up the android browser to improve UX
    // https://docs.expo.dev/guides/authentication/#improving-user-experience
    void WebBrowser.warmUpAsync();
    return () => {
      void WebBrowser.coolDownAsync();
    };
  }, []);
};

WebBrowser.maybeCompleteAuthSession();

const useAppAuth = () => {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut } = useClerk();
  const { t } = useTranslation();
  const { startSSOFlow } = useSSO();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [_user, setUser] = useState<MyUserProfileType | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(isLoaded);

  useEffect(() => {
    if (isLoaded) {
      setIsAuthenticated(isSignedIn);
      setUser(toMyUserProfile(user));
      setIsLoadingUser(false);
    }
  }, [isLoaded, isSignedIn, user]);

  const handleSignOut = () => {
    signOut()
      .catch(async (err) => {
        createHandleErrorDialog({
          title: "Erro ao terminar sessão",
          error: err,
        });
      })
      .then(() => router.replace("/sign-in"));
  };

  const deleteUser = async () => {
    try {
      setIsLoadingUser(true);
      if (!_user?.id) {
        throw new Error("User not found");
      }

      await fetchApi(API_URLS.users, {
        method: "DELETE",
        body: JSON.stringify({ userId: _user.id }),
      });

      await handleSignOut();
    } catch (err) {
      createHandleErrorDialog({
        title: "Erro ao excluir conta",
        message:
          "Ocorreu um erro ao excluir a sua conta. Tente novamente mais tarde.",
        error: err as Error,
      });
      throw err;
    } finally {
      setIsLoadingUser(false);
    }
  };

  const checkProfileComplete = ({
    user,
    t,
  }: {
    user: MyUserProfileType | null;
    t: TFunction<"translation", undefined>;
  }): boolean => {
    if (!user) return false;

    const schema = myUserProfileSchema(t).safeParse(user);
    return schema.success;
  };

  const handleGoogleOAuth = useCallback(async () => {
    try {
      const appScheme = Linking.createURL("").slice(0, -3); // Remove the trailing "://"
      console.log(appScheme);
      const { createdSessionId, setActive, signIn, signUp } =
        await startSSOFlow({
          strategy: "oauth_google",
          // For native, you must pass a scheme using AuthSession.makeRedirectUri
          // For more info, see https://docs.expo.dev/versions/latest/sdk/auth-session/#authsessionmakeredirecturioptions
          redirectUrl: AuthSession.makeRedirectUri({ scheme: "myapp" }),
        });

      // If sign in was successful, set the active session
      if (createdSessionId) {
        setActive!({ session: createdSessionId });
        router.replace("/(tabs)");
      } else {
        // If there is no `createdSessionId`,
        // there are missing requirements, such as MFA
        // Use the `signIn` or `signUp` returned from `startSSOFlow`
        // to handle next steps
      }
    } catch (err) {
      createHandleErrorDialog({
        title: t("auth.error_signing_in"),
        error: err as Error,
      });

      console.error("Google OAuth error", JSON.stringify(err, null, 2));
    }
  }, [startSSOFlow, t]);

  return {
    user: _user,
    isSignedIn: isAuthenticated,
    isLoadingUser,
    signOut: handleSignOut,
    deleteUser,
    SignedOut,
    SignedIn,
    userClient: user,
    isProfileComplete: () => checkProfileComplete({ user: _user, t }),
    handleGoogleOAuth,
    useWarmUpBrowser,
  };
};

export default useAppAuth;
