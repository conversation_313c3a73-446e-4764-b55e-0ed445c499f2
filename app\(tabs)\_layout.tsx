import useAppAuth from "@/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";
import Feather from "@expo/vector-icons/Feather";
import { Redirect, Tabs } from "expo-router";
import { useTranslation } from "react-i18next";

export default function TabLayout() {
  const { t } = useTranslation();
  const { isLoadingUser, isProfileComplete } = useAppAuth();

  if (!isLoadingUser && !isProfileComplete()) {
    return <Redirect href="/profile-edit" />;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: globalStyles.colors.primary2,
        tabBarInactiveTintColor: globalStyles.colors.light.secondary,
        tabBarStyle: {
          paddingBottom: 5,
          // paddingTop: 10,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          tabBarLabel: t("common.home"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="home" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          tabBarLabel: t("common.search"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="search" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          tabBarLabel: t("common.profile"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="user" color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
